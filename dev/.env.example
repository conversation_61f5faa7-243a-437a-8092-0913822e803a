# Kproximate Development Environment Configuration

# Debug mode
DEBUG=true

# Application configuration
POLL_INTERVAL=10
MAX_KP_NODES=3
LOAD_HEADROOM=0.2
WAIT_SECONDS_FOR_JOIN=120
WAIT_SECONDS_FOR_PROVISION=120

# Scale-down stabilization configuration
# Prevents scale-down if any node was created within this period (in minutes)
# This helps prevent infinite scale-up/scale-down cycles
SCALE_DOWN_STABILIZATION_MINUTES=5

# Minimum age in minutes before a node can be considered for scale-down
# This prevents newly created nodes from being immediately removed
MIN_NODE_AGE_MINUTES=10

# RabbitMQ configuration
RABBITMQ_CONTAINER_NAME=kproximate-rabbitmq
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_PORT=5672
RABBITMQ_MANAGEMENT_PORT=15672
# Set to false for local development (no TLS)
RABBITMQ_USE_TLS=false

# Proxmox configuration
# Required for full functionality
PM_URL=https://datacenter.mydomain.net:8006/api2/json
PM_USER_ID=kproximate@pam!kproximate
# Use either PM_PASSWORD or PM_TOKEN
PM_PASSWORD=your-password
PM_TOKEN=42adb612-906d-425e-a46a-aff2826162e2
PM_ALLOW_INSECURE=false
PM_DEBUG=false

# Node configuration
KP_NODE_TEMPLATE_NAME=ubuntu-22.04-cloudinit-template
KP_NODE_NAME_PREFIX=kp-node
KP_NODE_CORES=4
KP_NODE_MEMORY=4096
KP_NODE_DISABLE_SSH=false
KP_QEMU_EXEC_JOIN=true
KP_LOCAL_TEMPLATE_STORAGE=true

# Additional configuration
KP_NODE_LABELS=topology.kubernetes.io/region=proxmox-cluster,topology.kubernetes.io/zone={{ .TargetHost }}
# SSH_KEY=your-ssh-key

# Node Selection Strategy Configuration
# Strategy for selecting Proxmox hosts when scaling up
# Options: spread, max-memory, max-cpu, balanced, round-robin
NODE_SELECTION_STRATEGY=spread

# Minimum resource requirements for host eligibility
# Hosts that don't meet these requirements will be excluded from selection
MIN_AVAILABLE_CPU_CORES=0
MIN_AVAILABLE_MEMORY_MB=0

# Comma-separated list of Proxmox node names to exclude from scaling up
# Useful for maintenance, dedicated workloads, or resource constraints
EXCLUDED_NODES=""

# Kubernetes join command
# This is the command that will be executed on the new node to join the Kubernetes cluster
KP_JOIN_COMMAND=""

# Kubernetes configuration
# Path to kubeconfig file for accessing the Kubernetes cluster
# KUBECONFIG=/path/to/kubeconfig

version: 2.1

commands:
  setup_helm:
    steps:
      - run: curl -fsSL -o /tmp/get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
      - run: chmod 700 /tmp/get_helm.sh
      - run: /tmp/get_helm.sh

jobs:
  test:
    working_directory: ~/project/kproximate
    docker:
      - image: cimg/go:1.24
    steps:
      - checkout:
          path: ~/project
      - restore_cache:
          keys:
            - go-mod-v1-{{ checksum "go.sum" }}
            - go-mod-v1
      - run:
          name: Install Dependencies
          command: go get ./...
      - save_cache:
          key: go-mod-v1-{{ checksum "go.sum" }}
          paths:
            - "/go/pkg/mod"
      - run:
          name: Run tests
          command: go test ./...
  build_image:
    docker:
      - image: cimg/base:current
    parameters:
      component:
        type: string
    steps:
      - setup_remote_docker
      - checkout
      - run:
          name: Setup docker buildx
          command: docker buildx create --use --name multiarch --driver docker-container
      - run:
          name: Login to ghcr
          command: echo $GITHUB_TOKEN | docker login ghcr.io -u $GITHUB_PROJECT_USERNAME --password-stdin
      - run:
          name: Build & push << parameters.component >> image
          command: |
            # Ensure repo and tag are lowercase for docker
            REPO_LC=$(echo "$GITHUB_PROJECT_REPONAME" | tr '[:upper:]' '[:lower:]')
            USER_LC=$(echo "$GITHUB_PROJECT_USERNAME" | tr '[:upper:]' '[:lower:]')
            docker buildx build --platform linux/amd64,linux/arm64 -f Dockerfile --build-arg COMPONENT=<< parameters.component >> -t ghcr.io/$USER_LC/$REPO_LC-<< parameters.component >>:$CIRCLE_TAG --push .
  publish_helm_chart:
    docker:
      - image: cimg/base:current
    steps:
      - setup_helm
      - checkout
      - run:
          name: Package the helm chart
          command: |
            helm repo add bitnami https://charts.bitnami.com/bitnami
            helm dependency build ./chart/$GITHUB_PROJECT_REPONAME/
            helm package ./chart/$GITHUB_PROJECT_REPONAME --version $CIRCLE_TAG
      - run:
          name: Login to ghcr
          command: echo $GITHUB_TOKEN | docker login ghcr.io -u $GITHUB_PROJECT_USERNAME --password-stdin
      - run:
          name: Push the helm chart
          command: helm push $GITHUB_PROJECT_REPONAME-$CIRCLE_TAG.tgz oci://ghcr.io/$GITHUB_PROJECT_USERNAME

workflows:
  test_build_and_publish:
    jobs:
      - test:
          filters:
            tags:
              only: /\d+\.\d+\.\d+/
      - build_image:
          name: build << matrix.component >> image
          context:
            - github
          requires:
            - test
          matrix:
            parameters:
              component: ["controller", "worker"]
          filters:
            branches:
              ignore: /.*/
            tags:
              only: /\d+\.\d+\.\d+/
      - publish_helm_chart:
          context:
            - github
          requires:
            - test
            - build_image
          filters:
            branches:
              ignore: /.*/
            tags:
              only: /\d+\.\d+\.\d+/

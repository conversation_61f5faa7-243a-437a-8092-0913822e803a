apiVersion: v1
kind: Secret
metadata:
  name: {{ include "kproximate.fullname" . }}
type: Opaque
data:
  kpJoinCommand: {{ .Values.kproximate.secrets.kpJoinCommand | b64enc }}
  pmPassword: {{ .Values.kproximate.secrets.pmPassword | b64enc }}
  pmToken: {{ .Values.kproximate.secrets.pmToken | b64enc }}
  sshKey: {{ .Values.kproximate.secrets.sshKey | b64enc }}
  rabbitMQPassword: {{ .Values.rabbitmq.auth.password | b64enc | required ".Values.rabbitmq.auth.password is required" }}

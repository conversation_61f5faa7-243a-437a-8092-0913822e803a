apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kproximate.fullname" . }}
rules:
- apiGroups: [""]
  resources: ["pods/eviction"]
  verbs: ["create"]
# Needed to list pods by Node
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
# Needed to cordon Nodes
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "patch", "list", "update", "delete"]
# Needed to determine Pod owners
- apiGroups: ["apps"]
  resources: ["statefulsets"]
  verbs: ["get", "list"]
# Needed to determine Pod owners
- apiGroups: ["extensions"]
  resources: ["daemonsets", "replicasets"]
  verbs: ["get", "list"]
- apiGroups: ["storage.k8s.io"]
  resources: ["volumeattachments"]
  verbs: ["list", "delete"]

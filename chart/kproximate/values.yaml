# Default values for kproximate.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

kproximate:
  config:
    ## Verbose logging
    debug: false

    ## The period to wait for a provisioned node to join the kubernetes cluster before the scaling
    ## event is declared to have failed.
    waitSecondsForJoin: 120

    ## The period to wait for a new node to be provisioned and declared ready before the scaling
    ## event is declared to have failed.
    waitSecondsForProvision: 120

    ## The required headroom used in scale down calculations expressed as a value between 0
    ## and 1. If a value below 0.2 is specified it will be ignored and the default value
    ## is used.
    loadHeadroom: 0.2

    ## Scale-down stabilization period in minutes. Prevents scale-down if any node
    ## was created within this period.
    scaleDownStabilizationMinutes: 5

    ## Minimum age in minutes before a node can be considered for scale-down.
    ## This prevents newly created nodes from being immediately removed.
    minNodeAgeMinutes: 10

    ## The number of cores assigned to new kproximate nodes.
    kpNodeCores: 2

    ## The amount of memory assigned to new kproximate nodes in MiB.
    kpNodeMemory: 2048

    ## Set true to disable SSH key injection.
    kpNodeDisableSsh: false

    ## Set labels on kp nodes. A comma separated list of labels to apply to kp-nodes in
    ## the format "some.key=somevalue". It is also possible to use values only known at
    ## provisioning time using go templating. Currently the only supported template value
    ## is "targetHost" which is the name of the proxmox host on which the kp-node is to be
    ## provsioned e.g. "topology.kubernetes.io/zone={{ targetHost }}".
    kpNodeLabels: "topology.kubernetes.io/region=proxmox-cluster,topology.kubernetes.io/zone={{ .TargetHost }}"

    ## The prefix to use when naming new kproximate nodes.
    kpNodeNamePrefix: kp-node

    ## The name of the Proxmox template to use for new kproximate nodes.
    kpNodeTemplateName: null ## Required

    ## Set true to use Qemu-Exec to join nodes to the kubernetes cluster.
    kpQemuExecJoin: false

    ## Set true if using local storage for templates.
    kpLocalTemplateStorage: false

    ## The maximum number of kproximate nodes allowed.
    maxKpNodes: 3

    ## Set true to skip TLS checks for the Proxmox API.
    pmAllowInsecure: false

    ## Set true to enable debug output for Proxmox API calls.
    pmDebug: false

    ## The Proxmox API URL.
    pmUrl: null ## Required

    ## The Proxmox API token ID or user ID.
    pmUserID: null ## Required

    ## The number of seconds between polls of the cluster for scaling.
    pollInterval: 10

    # Node Selection Strategy Configuration
    # Available strategies:
    # - "spread": Distributes nodes across different Proxmox hosts (default)
    # - "max-memory": Selects host with most available memory
    # - "max-cpu": Selects host with most available CPU
    # - "balanced": Selects host with best combined CPU and memory availability
    # - "round-robin": Cycles through available hosts
    nodeSelectionStrategy: spread

    ## Minimum available CPU cores required for a Proxmox host to be eligible for scaling up
    minAvailableCpuCores: 0

    ## Minimum available memory in MB required for a Proxmox host to be eligible for scaling up
    minAvailableMemoryMB: 0

    ## Comma-separated list of Proxmox node names to exclude from scaling up
    excludedNodes: ""

    ## The rabbitmq service address
    rabbitMQHost: kproximate-rabbitmq

    ## The rabbitmq service port
    rabbitMQPort: 5671

  secrets:
    ## The command to use to join worker nodes to the kubernetes cluster.
    kpJoinCommand: ""

    ## The user's Proxmox password
    pmPassword: "" ## Required if pmToken not set

    ## The Proxmox API token for the token ID.
    pmToken: "" ## Required if pmPassword not set

    ## The SSH public key to inject into the kproximate template.
    sshKey: ""

workerReplicaCount: 3

rabbitmq:
  auth:
    username: kproximate
    ## The password to use to configure and access rabbitmq.
    password: null ## Required
    tls:
      enabled: true
      autoGenerated: true
      failIfNoPeerCert: false
  persistence:
    enabled: false
  extraPlugins: ""
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 1
          preference:
            matchExpressions:
              - key: node-role.kubernetes.io/control-plane
                operator: Exists
              - key: node-role.kubernetes.io/master
                operator: Exists
  tolerations:
    - key: node-role.kubernetes.io/control-plane
      operator: Exists
      effect: NoSchedule
    - key: node-role.kubernetes.io/master
      operator: Exists
      effect: NoSchedule

image:
  registry: ghcr.io/paradoxe35
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: ""

controllerPodAnnotations: {}
workerPodAnnotations: {}

podSecurityContext: {} # fsGroup: 2000

securityContext: {}
# capabilities:
#   drop:
#   - ALL
# readOnlyRootFilesystem: true
# runAsNonRoot: true
# runAsUser: 1000

resources: {}
# We usually recommend not to specify default resources and to leave this as a conscious
# choice for the user. This also increases chances charts run on environments with little
# resources, such as Minikube. If you do want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
# limits:
#   cpu: 100m
#   memory: 128Mi
# requests:
#   cpu: 100m
#   memory: 128Mi

nodeSelector: {}

tolerations: []

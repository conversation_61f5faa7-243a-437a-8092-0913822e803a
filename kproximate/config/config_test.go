package config

import (
	"testing"
)

func TestValidateConfig(t *testing.T) {
	cfg := &KproximateConfig{
		LoadHeadroom:            0.1,
		PollInterval:            5,
		WaitSecondsForJoin:      30,
		WaitSecondsForProvision: 30,
	}

	*cfg = validateConfig(cfg)

	if cfg.LoadHeadroom != 0.2 {
		t.<PERSON><PERSON>("Expected \"LoadHeadroom\" to be 0.2, got %f", cfg.LoadHeadroom)
	}

	if cfg.PollInterval != 10 {
		t.<PERSON><PERSON><PERSON>("Expected \"PollInterval\" to be10, got %d", cfg.PollInterval)
	}

	if cfg.WaitSecondsForJoin != 60 {
		t.<PERSON><PERSON><PERSON>("Expected \"WaitSecondsForJoin\" to be 60, got %d", cfg.WaitSecondsForJoin)
	}

	if cfg.WaitSecondsForProvision != 60 {
		t.<PERSON><PERSON><PERSON>("Expected \"WaitSecondsForProvision\" to be 60, got %d", cfg.WaitSecondsForProvision)
	}
}
